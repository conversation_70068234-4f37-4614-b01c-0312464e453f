<!DOCTYPE html>
<html>
<head>
    <title>Audio Handler Test</title>
</head>
<body>
    <h1>Audio Handler Test</h1>
    <p>Open the browser console and run:</p>
    <ul>
        <li><code>window.testAudio.speakText("Hello world")</code> - Test TTS</li>
        <li><code>window.testAudio.stopAudio()</code> - Stop all audio</li>
    </ul>
    
    <script>
        // This file is just for documentation
        // The actual test functions are available when AudioHandler component is loaded
        console.log('Load the AudioHandler component to access test functions');
    </script>
</body>
</html>

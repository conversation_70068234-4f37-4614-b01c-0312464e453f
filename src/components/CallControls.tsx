'use client';

import { useRef } from 'react';
import { CallTooltip } from './CallTooltip';

interface CallControlsProps {
  isMuted: boolean;
  isVideoOn: boolean;
  isScreenSharing: boolean;
  onToggleMute?: () => void;
  onToggleVideo?: () => void;
  onToggleScreenShare?: () => void;
  onEndCall: () => void;
}

export default function CallControls({
  isMuted = false,
  isVideoOn = true,
  isScreenSharing = false,
  onToggleMute,
  onToggleVideo,
  onToggleScreenShare,
  onEndCall
}: CallControlsProps) {
  return (
    <div className="border-t border-[#2e2e2e] bg-[#1e1e1e] w-full h-[60px] py-2">
      <div className="grid grid-cols-3 items-center max-w-6xl mx-auto px-6">
        {/* Left controls */}
        <div className="flex items-center space-x-4 justify-start">
          {/* Mic button */}
          <CallTooltip content={isMuted ? "Unmute" : "Mute"}>
            <button 
              onClick={onToggleMute}
              className={`w-10 h-10 rounded-[6px] ${isMuted ? 'bg-[#6466E9]' : 'bg-[#222222]'} flex items-center justify-center text-white hover:bg-[#333333] transition-colors border border-[#333333]`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {isMuted ? (
                  <>
                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </>
                ) : (
                  <>
                    <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                    <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
                  </>
                )}
              </svg>
            </button>
          </CallTooltip>
          
          {/* Camera button */}
          <CallTooltip content={isVideoOn ? "Turn off camera" : "Turn on camera"}>
            <button 
              onClick={onToggleVideo}
              className={`w-10 h-10 rounded-[6px] ${!isVideoOn ? 'bg-[#6466E9]' : 'bg-[#222222]'} flex items-center justify-center text-white hover:bg-[#333333] transition-colors border border-[#333333]`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                {!isVideoOn ? (
                  <>
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                    <line x1="1" y1="1" x2="23" y2="23"></line>
                  </>
                ) : (
                  <>
                    <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                  </>
                )}
              </svg>
            </button>
          </CallTooltip>
          
          {/* Screen share button */}
          <CallTooltip content={isScreenSharing ? "Stop sharing" : "Share screen"}>
            <button 
              onClick={onToggleScreenShare}
              className={`w-10 h-10 rounded-[6px] ${isScreenSharing ? 'bg-[#6466E9]' : 'bg-[#222222]'} flex items-center justify-center text-white hover:bg-[#333333] transition-colors border border-[#333333]`}
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                <path d="M8 21h8"></path>
                <path d="M12 17v4"></path>
                <path d="M12 12V8"></path>
                <path d="M16 12l-4-4-4 4"></path>
              </svg>
            </button>
          </CallTooltip>
        </div>
        
        {/* Center - End call button */}
        <div className="flex justify-center">
          <CallTooltip content="End call">
            <button 
              onClick={onEndCall}
              className="px-6 h-10 rounded-[6px] bg-[#CC4D4C] bg-hover-[#e11d48] text-white hover:bg-[#c81e42] transition-colors border-none flex items-center justify-center"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="mr-2">
                <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                <line x1="4.5" y1="19.5" x2="19.5" y2="4.5"></line>
              </svg>
              End Call
            </button>
          </CallTooltip>
        </div>
        
        {/* Right controls */}
        <div className="flex items-center space-x-4 justify-end">
          {/* More options button */}
          <CallTooltip content="More options">
            <button className="w-10 h-10 rounded-[6px] bg-[#222222] flex items-center justify-center text-white hover:bg-[#333333] transition-colors border border-[#333333]">
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="1"></circle>
                <circle cx="12" cy="5" r="1"></circle>
                <circle cx="12" cy="19" r="1"></circle>
              </svg>
            </button>
          </CallTooltip>
        </div>
      </div>
    </div>
  );
}

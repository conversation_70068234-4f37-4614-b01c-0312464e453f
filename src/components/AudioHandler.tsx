'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';

interface AudioHandlerProps {
  isListening: boolean;
  onSpeechResult: (transcript: string) => void;
  onSpeechEnd: () => void;
  onError: (error: string) => void;
}

declare global {
  interface Window {
    SpeechRecognition: any;
    webkitSpeechRecognition: any;
  }
}

export default function AudioHandler({
  isListening,
  onSpeechResult,
  onSpeechEnd,
  onError
}: AudioHandlerProps) {
  const recognitionRef = useRef<any>(null);
  const [isSupported, setIsSupported] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');

  useEffect(() => {
    // Check if speech recognition is supported
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;

    if (SpeechRecognition) {
      setIsSupported(true);

      // Initialize speech recognition
      const recognition = new SpeechRecognition();
      recognition.continuous = true;
      recognition.interimResults = true;
      recognition.lang = 'en-US';
      recognition.maxAlternatives = 1;

      recognition.onstart = () => {
        console.log('Speech recognition started');
      };

      recognition.onresult = (event: any) => {
        let finalTranscript = '';
        let interimTranscript = '';

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript;
          if (event.results[i].isFinal) {
            finalTranscript += transcript;
          } else {
            interimTranscript += transcript;
          }
        }

        const fullTranscript = finalTranscript || interimTranscript;
        setCurrentTranscript(fullTranscript);

        if (finalTranscript) {
          onSpeechResult(finalTranscript.trim());
        }
      };

      recognition.onerror = (event: any) => {
        console.error('Speech recognition error:', event.error);
        onError(`Speech recognition error: ${event.error}`);
      };

      recognition.onend = () => {
        console.log('Speech recognition ended');
        onSpeechEnd();
        setCurrentTranscript('');
        // Restart automatically to keep listening
        try {
          recognition.start();
        } catch (error) {
          console.error('Error restarting speech recognition:', error);
        }
      };

      recognitionRef.current = recognition;

      // Start listening immediately
      try {
        recognition.start();
      } catch (error) {
        console.error('Error starting speech recognition:', error);
      }
    } else {
      setIsSupported(false);
      onError('Speech recognition is not supported in this browser');
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop();
      }
    };
  }, [onSpeechResult, onSpeechEnd, onError]);

  // Keep track of the last request to prevent duplicates
let lastRequestId = '';
let lastRequestTime = 0;
const REQUEST_DEBOUNCE_TIME = 1000; // 1 second debounce

// Global audio playback lock to prevent multiple simultaneous audio playbacks
let isAudioPlaybackInProgress = false;
let pendingAudioRequests: Array<{text: string, resolve: () => void, reject: (error: Error) => void}> = [];
let currentAudioRequestId: string | null = null;

const speakText = useCallback(async (text: string): Promise<void> => {
    return new Promise((resolve, reject) => {
      // Generate a unique request ID for this speech request
      const requestId = `speech_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
      
      // Debounce duplicate requests
      const now = Date.now();
      if (text === lastRequestId && now - lastRequestTime < REQUEST_DEBOUNCE_TIME) {
        console.log('Debouncing duplicate speech request');
        resolve(); // Resolve immediately for duplicate requests
        return;
      }
      
      // Update last request tracking
      lastRequestId = text;
      lastRequestTime = now;
      
      // If audio is already playing, queue this request
      if (isAudioPlaybackInProgress) {
        console.log('Audio already playing, queueing request');
        pendingAudioRequests.push({
          text,
          resolve,
          reject
        });
        return;
      }
      
      // Set the global playback lock
      isAudioPlaybackInProgress = true;
      currentAudioRequestId = requestId;
      console.log(`Starting speech synthesis for request: ${requestId}`);
      
      // Try ElevenLabs first
      synthesizeWithElevenLabs(text, requestId)
        .then(() => {
          // Success with ElevenLabs
          isAudioPlaybackInProgress = false;
          currentAudioRequestId = null;
          resolve();
          
          // Process next request in queue if any
          if (pendingAudioRequests.length > 0) {
            const nextRequest = pendingAudioRequests.shift()!;
            speakText(nextRequest.text)
              .then(nextRequest.resolve)
              .catch(nextRequest.reject);
          }
        })
        .catch((error) => {
          console.warn('ElevenLabs synthesis failed, falling back to browser TTS:', error);
          
          // Fallback to browser TTS
          if (!('speechSynthesis' in window)) {
            isAudioPlaybackInProgress = false;
            currentAudioRequestId = null;
            reject(new Error('Speech synthesis not supported'));
            return;
          }

          // Cancel any ongoing speech
          window.speechSynthesis.cancel();

          const utterance = new SpeechSynthesisUtterance(text);
          
          // Configure voice settings
          utterance.rate = 0.9;
          utterance.pitch = 1.0;
          utterance.volume = 1.0;

          // Try to use a professional-sounding voice
          const voices = window.speechSynthesis.getVoices();
          const preferredVoice = voices.find(voice => 
            voice.name.includes('Alex') || 
            voice.name.includes('Daniel') || 
            voice.name.includes('Google US English')
          );
          
          if (preferredVoice) {
            utterance.voice = preferredVoice;
          }

          utterance.onend = () => {
            isAudioPlaybackInProgress = false;
            currentAudioRequestId = null;
            resolve();
            
            // Process next request in queue if any
            if (pendingAudioRequests.length > 0) {
              const nextRequest = pendingAudioRequests.shift()!;
              speakText(nextRequest.text)
                .then(nextRequest.resolve)
                .catch(nextRequest.reject);
            }
          };

          utterance.onerror = (event) => {
            isAudioPlaybackInProgress = false;
            currentAudioRequestId = null;
            reject(new Error(`Speech synthesis error: ${event.error}`));
          };

          window.speechSynthesis.speak(utterance);
        });
    });
  }, []);
  
  // Function to synthesize speech using ElevenLabs API
  const synthesizeWithElevenLabs = useCallback(async (text: string, requestId?: string): Promise<void> => {
    return new Promise(async (resolve, reject) => {
      try {
        // Generate a request ID if not provided
        const actualRequestId = requestId || `synthesis_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
        console.log(`Requesting voice synthesis with ID: ${actualRequestId}`);
        
        // Check if we should still be playing audio
        if (!isAudioPlaybackInProgress) {
          console.warn(`Audio playback state changed before synthesis request was sent. Aborting request ${actualRequestId}`);
          reject(new Error('Audio playback state changed'));
          return;
        }
        
        // Add a flag to indicate this is a new synthesis request, not a playback of cached audio
        const controller = new AbortController();
        const timeoutId = setTimeout(() => {
          controller.abort();
        }, 10000); // 10 second timeout
        
        try {
          const response = await fetch('/api/voice', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ 
              text,
              requestId: actualRequestId, // Include request ID for caching
              isPlayingAudio: isAudioPlaybackInProgress // Tell the server if audio is already playing
            }),
            signal: controller.signal
          });
          
          clearTimeout(timeoutId);

          if (!response.ok) {
            throw new Error(`Voice synthesis error: ${response.status} ${response.statusText}`);
          }

          const audioBlob = await response.blob();
          console.log(`Received audio blob of size: ${audioBlob.size} bytes`);
          
          if (audioBlob.size === 0) {
            console.error('Received empty audio blob from server');
            reject(new Error('Empty audio blob received'));
            return;
          }
          
          const audioUrl = URL.createObjectURL(audioBlob);
          console.log(`Created audio URL: ${audioUrl}`);
          
          const audio = new Audio(audioUrl);

          // Preload the audio before playing
          audio.preload = 'auto';
          
          // Add more event listeners for debugging
          audio.onloadedmetadata = () => {
            console.log(`Audio metadata loaded, duration: ${audio.duration}s`);
          };
          
          audio.oncanplay = () => {
            console.log('Audio can play now, starting playback...');
          };

          audio.onended = () => {
            console.log('ElevenLabs audio playback completed');
            URL.revokeObjectURL(audioUrl);
            resolve();
          };

          audio.onerror = () => {
            console.error(`Audio playback error:`, audio.error);
            URL.revokeObjectURL(audioUrl);
            reject(new Error(`Audio playback error: ${audio.error?.message || 'Unknown error'}`));
          };

          console.log(`Starting playback for synthesis request: ${actualRequestId}`);
          try {
            await audio.play();
            console.log('Audio playback started successfully');
          } catch (playError) {
            console.error('Error playing audio (likely autoplay policy):', playError);
            URL.revokeObjectURL(audioUrl);
            
            // Try again with user interaction
            const userInteractionPrompt = document.createElement('div');
            userInteractionPrompt.style.position = 'fixed';
            userInteractionPrompt.style.top = '20px';
            userInteractionPrompt.style.left = '50%';
            userInteractionPrompt.style.transform = 'translateX(-50%)';
            userInteractionPrompt.style.padding = '10px';
            userInteractionPrompt.style.backgroundColor = '#333';
            userInteractionPrompt.style.color = '#fff';
            userInteractionPrompt.style.borderRadius = '5px';
            userInteractionPrompt.style.zIndex = '9999';
            userInteractionPrompt.textContent = 'Click to enable audio';
            
            userInteractionPrompt.onclick = async () => {
              try {
                await audio.play();
                document.body.removeChild(userInteractionPrompt);
              } catch (err) {
                console.error('Still failed to play after user interaction:', err);
                document.body.removeChild(userInteractionPrompt);
                reject(err);
              }
            };
            
            document.body.appendChild(userInteractionPrompt);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
              if (document.body.contains(userInteractionPrompt)) {
                document.body.removeChild(userInteractionPrompt);
                reject(new Error('Audio playback failed - autoplay policy'));
              }
            }, 5000);
          }
        } catch (fetchError) {
          clearTimeout(timeoutId);
          console.error(`Fetch error for synthesis request ${actualRequestId}:`, fetchError);
          reject(fetchError);
        }
      } catch (error) {
        console.error('Error in synthesizeWithElevenLabs:', error);
        reject(error);
      }
    });
  }, []);

  // Expose speakText function to parent components and add test functions
  useEffect(() => {
    (window as any).pitchpalSpeakText = speakText;
    
    // Add test functions for debugging
    (window as any).testAudio = {
      speakText: (text: string = 'This is a test of the audio system. Can you hear me now?') => {
        console.log('Testing audio with text:', text);
        speakText(text);
      },
      synthesizeWithElevenLabs: (text: string = 'This is a test of the ElevenLabs synthesis. Can you hear me now?') => {
        console.log('Testing ElevenLabs synthesis with text:', text);
        synthesizeWithElevenLabs(text);
      },
      useNativeTTS: (text: string = 'This is a test of the native browser TTS. Can you hear me now?') => {
        console.log('Testing native TTS with text:', text);
        if (!('speechSynthesis' in window)) {
          console.error('Speech synthesis not supported');
          return;
        }
        window.speechSynthesis.cancel();
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.rate = 0.9;
        utterance.pitch = 1.0;
        utterance.volume = 1.0;
        window.speechSynthesis.speak(utterance);
      }
    };
    
    console.log('Audio test functions available. Try window.testAudio.speakText() in the console');
    
    return () => {
      delete (window as any).pitchpalSpeakText;
      delete (window as any).testAudio;
    };
  }, [speakText, synthesizeWithElevenLabs]);

  if (!isSupported) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg">
        <p className="text-sm">
          Speech recognition is not supported in this browser. 
          Please use Chrome, Edge, or Safari for the best experience.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Live transcript display - subtle and clean */}
      {currentTranscript && isListening && (
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 0.9, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-black/40 backdrop-blur-sm text-white px-4 py-2 rounded-full max-w-md shadow-sm border border-white/10"
        >
          <div className="flex items-center space-x-2">
            <div className="text-xs text-white/80 font-light">{currentTranscript}</div>
          </div>
        </motion.div>
      )}
    </>
  );
}

// Keep track of the last request to prevent duplicates
let lastRequestId = '';
let lastRequestTime = 0;
const REQUEST_DEBOUNCE_TIME = 1000; // 1 second debounce

// Utility function to synthesize speech using ElevenLabs API
export async function synthesizeWithElevenLabs(text: string): Promise<void> {
  // Generate a unique request ID based on text and timestamp
  const now = Date.now();
  const requestId = `${text}_${now}`;
  
  // Check if this is a duplicate request (same text within debounce time)
  if (now - lastRequestTime < REQUEST_DEBOUNCE_TIME && text === lastRequestId.split('_')[0]) {
    console.log('Debouncing duplicate voice request:', text);
    return;
  }
  
  // Update last request tracking
  lastRequestId = requestId;
  lastRequestTime = now;
  
  try {
    console.log('Synthesizing voice for:', text.substring(0, 30) + '...');
    const response = await fetch('/api/voice', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ text, requestId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(`Voice synthesis failed: ${errorData.error || response.statusText}`);
    }

    // Check if response is audio data
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('audio')) {
      // Response is audio data, play it directly
      const audioBlob = await response.blob();
      const audioUrl = URL.createObjectURL(audioBlob);
      await playAudio(audioUrl);
      URL.revokeObjectURL(audioUrl);
    } else {
      // Fallback to browser speech synthesis
      throw new Error('No audio data received from ElevenLabs');
    }
  } catch (error) {
    console.error('ElevenLabs synthesis error:', error);
    // Fallback to browser speech synthesis
    if ('speechSynthesis' in window) {
      return new Promise((resolve, reject) => {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.onend = () => resolve();
        utterance.onerror = () => reject(new Error('Browser synthesis failed'));
        window.speechSynthesis.speak(utterance);
      });
    }
    throw error;
  }
}

// Utility function to play audio from URL
export function playAudio(audioUrl: string): Promise<void> {
  return new Promise((resolve, reject) => {
    const audio = new Audio(audioUrl);
    
    audio.onended = () => resolve();
    audio.onerror = () => reject(new Error('Audio playback failed'));
    
    audio.play().catch(reject);
  });
}

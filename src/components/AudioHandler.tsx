'use client';

import { useEffect, useRef, useState, useCallback } from 'react';
import { motion } from 'framer-motion';

interface AudioHandlerProps {
  isListening: boolean;
  onSpeechResult: (transcript: string) => void;
  onSpeechEnd: () => void;
  onError: (error: string) => void;
}

// Audio playback management
class AudioManager {
  private static instance: AudioManager;
  private isPlaying = false;
  private currentAudio: HTMLAudioElement | null = null;
  private audioQueue: Array<{ text: string; resolve: () => void; reject: (error: Error) => void }> = [];

  static getInstance(): AudioManager {
    if (!AudioManager.instance) {
      AudioManager.instance = new AudioManager();
    }
    return AudioManager.instance;
  }

  async playText(text: string): Promise<void> {
    return new Promise((resolve, reject) => {
      // If already playing, queue this request
      if (this.isPlaying) {
        console.log('Audio already playing, queueing request');
        this.audioQueue.push({ text, resolve, reject });
        return;
      }

      this.executePlayback(text, resolve, reject);
    });
  }

  private async executePlayback(text: string, resolve: () => void, reject: (error: Error) => void) {
    this.isPlaying = true;

    try {
      await this.synthesizeWithElevenLabs(text);
      resolve();
    } catch (error) {
      console.warn('ElevenLabs failed, falling back to browser TTS:', error);
      try {
        await this.fallbackToBrowserTTS(text);
        resolve();
      } catch (fallbackError) {
        reject(fallbackError as Error);
      }
    } finally {
      this.isPlaying = false;
      this.processQueue();
    }
  }

  private processQueue() {
    if (this.audioQueue.length > 0) {
      const next = this.audioQueue.shift()!;
      this.executePlayback(next.text, next.resolve, next.reject);
    }
  }

  private async synthesizeWithElevenLabs(text: string): Promise<void> {
    const response = await fetch('/api/voice', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ text }),
    });

    if (!response.ok) {
      throw new Error(`Voice synthesis error: ${response.status}`);
    }

    const audioBlob = await response.blob();
    if (audioBlob.size === 0) {
      throw new Error('Empty audio blob received');
    }

    return this.playAudioBlob(audioBlob);
  }

  private async fallbackToBrowserTTS(text: string): Promise<void> {
    if (!('speechSynthesis' in window)) {
      throw new Error('Speech synthesis not supported');
    }

    return new Promise((resolve, reject) => {
      window.speechSynthesis.cancel();
      const utterance = new SpeechSynthesisUtterance(text);
      utterance.rate = 0.9;
      utterance.pitch = 1.0;
      utterance.volume = 1.0;

      utterance.onend = () => resolve();
      utterance.onerror = (event) => reject(new Error(`Speech synthesis error: ${event.error}`));

      window.speechSynthesis.speak(utterance);
    });
  }

  private async playAudioBlob(audioBlob: Blob): Promise<void> {
    return new Promise((resolve, reject) => {
      const audioUrl = URL.createObjectURL(audioBlob);
      const audio = new Audio(audioUrl);

      this.currentAudio = audio;

      audio.onended = () => {
        URL.revokeObjectURL(audioUrl);
        this.currentAudio = null;
        resolve();
      };

      audio.onerror = () => {
        URL.revokeObjectURL(audioUrl);
        this.currentAudio = null;
        reject(new Error('Audio playback failed'));
      };

      audio.play().catch(reject);
    });
  }

  stop() {
    if (this.currentAudio) {
      this.currentAudio.pause();
      this.currentAudio = null;
    }
    this.isPlaying = false;
    this.audioQueue = [];
  }
}

export default function AudioHandler({
  isListening,
  onSpeechResult,
  onSpeechEnd,
  onError
}: AudioHandlerProps) {
  const wsRef = useRef<WebSocket | null>(null);
  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const [isSupported, setIsSupported] = useState(false);
  const [currentTranscript, setCurrentTranscript] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const audioManagerRef = useRef<AudioManager>(AudioManager.getInstance());

  // Initialize Deepgram WebSocket connection
  const initializeDeepgram = useCallback(async () => {
    try {
      // Get Deepgram token from our API
      const tokenResponse = await fetch('/api/deepgram');
      if (!tokenResponse.ok) {
        throw new Error('Failed to get Deepgram token');
      }
      const { token } = await tokenResponse.json();

      // Initialize WebSocket connection to Deepgram
      const ws = new WebSocket(
        `wss://api.deepgram.com/v1/listen?model=nova-2&language=en-US&smart_format=true&interim_results=true&endpointing=300`,
        ['token', token]
      );

      ws.onopen = () => {
        console.log('Deepgram WebSocket connected');
        setIsConnected(true);
        setIsSupported(true);
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);

        if (data.channel?.alternatives?.[0]?.transcript) {
          const transcript = data.channel.alternatives[0].transcript;

          if (transcript.trim()) {
            setCurrentTranscript(transcript);

            // Check if this is a final result
            if (data.is_final) {
              onSpeechResult(transcript.trim());
              setCurrentTranscript('');
              onSpeechEnd();
            }
          }
        }
      };

      ws.onerror = (error) => {
        console.error('Deepgram WebSocket error:', error);
        onError('Speech recognition connection error');
        setIsConnected(false);
      };

      ws.onclose = () => {
        console.log('Deepgram WebSocket closed');
        setIsConnected(false);
        // Attempt to reconnect after a delay
        setTimeout(() => {
          if (isListening) {
            initializeDeepgram();
          }
        }, 2000);
      };

      wsRef.current = ws;

      // Start audio capture
      const stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: 16000,
          channelCount: 1,
          echoCancellation: true,
          noiseSuppression: true
        }
      });

      const mediaRecorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm;codecs=opus'
      });

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0 && ws.readyState === WebSocket.OPEN) {
          ws.send(event.data);
        }
      };

      mediaRecorder.start(100); // Send data every 100ms
      mediaRecorderRef.current = mediaRecorder;

    } catch (error) {
      console.error('Error initializing Deepgram:', error);
      onError('Failed to initialize speech recognition');
      setIsSupported(false);
    }
  }, [isListening, onSpeechResult, onError]);

  useEffect(() => {
    if (isListening && !isConnected) {
      initializeDeepgram();
    }

    return () => {
      // Cleanup on unmount
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (mediaRecorderRef.current && mediaRecorderRef.current.state !== 'inactive') {
        mediaRecorderRef.current.stop();
      }
    };
  }, [isListening, isConnected, initializeDeepgram]);

  // Expose speakText function using AudioManager
  const speakText = useCallback(async (text: string): Promise<void> => {
    return audioManagerRef.current.playText(text);
  }, []);


  // Expose speakText function to parent components and add test functions
  useEffect(() => {
    (window as any).pitchpalSpeakText = speakText;

    // Add test functions for debugging
    (window as any).testAudio = {
      speakText: (text: string = 'This is a test of the audio system. Can you hear me now?') => {
        console.log('Testing audio with text:', text);
        speakText(text);
      },
      stopAudio: () => {
        console.log('Stopping all audio');
        audioManagerRef.current.stop();
      }
    };

    console.log('Audio test functions available. Try window.testAudio.speakText() in the console');

    return () => {
      delete (window as any).pitchpalSpeakText;
      delete (window as any).testAudio;
    };
  }, [speakText]);

  if (!isSupported) {
    return (
      <div className="fixed bottom-4 right-4 bg-red-600 text-white p-4 rounded-lg shadow-lg">
        <p className="text-sm">
          Speech recognition is not available.
          Please check your microphone permissions and internet connection.
        </p>
      </div>
    );
  }

  return (
    <>
      {/* Live transcript display - subtle and clean */}
      {currentTranscript && isListening && (
        <motion.div 
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 0.9, y: 0 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.2 }}
          className="fixed bottom-24 left-1/2 transform -translate-x-1/2 bg-black/40 backdrop-blur-sm text-white px-4 py-2 rounded-full max-w-md shadow-sm border border-white/10"
        >
          <div className="flex items-center space-x-2">
            <div className="text-xs text-white/80 font-light">{currentTranscript}</div>
          </div>
        </motion.div>
      )}
    </>
  );
}
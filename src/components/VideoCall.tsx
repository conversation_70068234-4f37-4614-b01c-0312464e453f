'use client';

import { useRef, useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Image from 'next/image'; // Added for logo
import CallControls from './CallControls';

interface VideoCallProps {
  isActive: boolean;
  currentQuestion: string;
  onStartCall: () => void;
  onEndCall: () => void;
  userData?: {
    name: string;
    startupName: string;
  };
  currentLevel?: number;
  levelTitle?: string;
}

export default function VideoCall({
  isActive,
  currentQuestion,
  onStartCall,
  onEndCall,
  userData,
  currentLevel,
  levelTitle
}: VideoCallProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  // Track camera permission state with a more explicit type
  type CameraPermissionState = 'pending' | 'granted' | 'denied';
  const [permissionState, setPermissionState] = useState<CameraPermissionState>('pending');
  const [isListening, setIsListening] = useState(false);
  const [isAISpeaking, setIsAISpeaking] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [isVideoOn, setIsVideoOn] = useState(true);
  const [isScreenSharing, setIsScreenSharing] = useState(false);
  const [callDuration, setCallDuration] = useState(0);
  const timerIntervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Only initialize camera when needed
    if (!isActive) {
      initializeCamera();
    }
    
    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }
    };
  }, []);
  
  // Call duration timer
  useEffect(() => {
    if (isActive) {
      // Reset timer when call starts
      setCallDuration(0);
      
      // Start the timer
      timerIntervalRef.current = setInterval(() => {
        setCallDuration(prev => prev + 1);
      }, 1000);
    } else {
      // Clear the timer when call ends
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }
    }
    
    // Cleanup on unmount
    return () => {
      if (timerIntervalRef.current) {
        clearInterval(timerIntervalRef.current);
        timerIntervalRef.current = null;
      }
    };
  }, [isActive]);
  
  // Handle audio muting
  useEffect(() => {
    if (stream) {
      const audioTracks = stream.getAudioTracks();
      audioTracks.forEach(track => {
        track.enabled = !isMuted;
      });
    }
  }, [stream, isMuted]);
  
  // Handle video toggling
  useEffect(() => {
    if (stream) {
      const videoTracks = stream.getVideoTracks();
      videoTracks.forEach(track => {
        track.enabled = isVideoOn;
      });
    }
  }, [stream, isVideoOn]);
  
  // Format call duration (seconds -> MM:SS)
  const formatCallDuration = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };
  
  // Handle screen sharing
  const handleScreenShare = async () => {
    try {
      if (isScreenSharing) {
        // Stop screen sharing and revert to camera
        if (stream) {
          // Stop all screen share tracks
          stream.getTracks().forEach(track => {
            if (track.kind === 'video') {
              track.stop();
            }
          });
        }
        // Reinitialize camera
        await initializeCamera();
      } else {
        // Start screen sharing
        const screenStream = await navigator.mediaDevices.getDisplayMedia({
          video: true,
          audio: true
        });
        
        // Replace video track with screen share track
        if (stream) {
          const videoTracks = stream.getVideoTracks();
          if (videoTracks.length > 0) {
            stream.removeTrack(videoTracks[0]);
          }
          
          screenStream.getVideoTracks().forEach(track => {
            stream.addTrack(track);
          });
          
          // Handle when user stops sharing via browser UI
          screenStream.getVideoTracks()[0].onended = () => {
            setIsScreenSharing(false);
            initializeCamera();
          };
        } else {
          setStream(screenStream);
        }
        
        if (videoRef.current) {
          videoRef.current.srcObject = stream;
        }
      }
    } catch (error) {
      console.error('Error toggling screen share:', error);
      setIsScreenSharing(false);
    }
  };

  const initializeCamera = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true
      });
      
      setStream(mediaStream);
      setPermissionState('granted');
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      
      // Only auto-trigger start after permissions are granted
      if (permissionState === 'pending') {
        // Small delay to ensure UI updates first
        setTimeout(() => {
          onStartCall();
        }, 500);
      }
    } catch (error) {
      console.error('Camera access error:', error);
      setPermissionState('denied');
    }
  };

  if (permissionState === 'denied') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center text-white">
          <div className="text-6xl mb-4">📹</div>
          <h2 className="text-2xl font-bold mb-4">Camera Access Required</h2>
          <p className="text-gray-400 mb-6">
            Please allow camera and microphone access to start your pitch practice.
          </p>
          <button
            onClick={initializeCamera}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg"
          >
            Grant Access
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen bg-[#121212] flex flex-col overflow-hidden">

      <div className="bg-[#1e1e1e] border-b border-[#2e2e2e] text-white h-[60px] px-4 flex justify-between items-center">
        <div className="flex items-center space-x-2">
          <Image src="/logo.svg" alt="Pitchly Logo" width={24} height={24} className="mr-2" />
          <span className="font-medium">{userData?.startupName || 'Pitchly'}</span>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-xs text-gray-400">
            {currentLevel && levelTitle ? `Level ${currentLevel}: ${levelTitle}` : 'Interview'}
          </span>
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
          <span className="text-xs">{formatCallDuration(callDuration)}</span>
        </div>
      </div>
      
      {/* Main video area */}
      <div className="flex-1 bg-[#121212] overflow-hidden p-3 flex items-center justify-center">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 w-full auto-rows-fr max-w-7xl mx-auto place-items-center">
          {/* User video */}
          <div className="relative bg-[#1e1e1e] rounded-lg overflow-hidden w-full h-full">
            <video
              ref={videoRef}
              autoPlay
              muted
              playsInline
              className="w-full h-full object-cover"
            />
            
            {/* User label */}
            <div className="absolute bottom-3 left-3 bg-black/70 text-white px-2 py-1 rounded-md text-sm backdrop-blur-sm flex items-center space-x-1">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              </svg>
              <span>You</span>
            </div>
            
            {/* Listening indicator - subtle and clean */}
            <AnimatePresence>
              {isListening && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 0.8 }}
                  exit={{ opacity: 0 }}
                  transition={{ duration: 0.3 }}
                  className="absolute top-3 right-3 flex items-center space-x-1.5"
                >
                  <div className="bg-black/40 backdrop-blur-sm px-2 py-0.5 rounded-full flex items-center space-x-1.5 border border-white/10">
                    <motion.div
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut" }}
                      className="w-1 h-1 bg-green-400 rounded-full"
                    />
                    <motion.div
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut", delay: 0.2 }}
                      className="w-1 h-1 bg-green-400 rounded-full"
                    />
                    <motion.div
                      animate={{ opacity: [0.5, 1, 0.5] }}
                      transition={{ repeat: Infinity, duration: 1.5, ease: "easeInOut", delay: 0.4 }}
                      className="w-1 h-1 bg-green-400 rounded-full"
                    />
                    <span className="text-[10px] text-green-400 font-light">Listening</span>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* AI/VC video area */}
          <div className="relative bg-[#1e1e1e] rounded-lg overflow-hidden flex items-center justify-center w-full h-full">
            <div className="text-center text-white py-8">
              {/* AI Avatar */}
              <motion.div
                animate={isAISpeaking ? { scale: [1, 1.05, 1] } : {}}
                transition={{ repeat: isAISpeaking ? Infinity : 0, duration: 2 }}
                className="w-24 h-24 bg-[#6466E9] rounded-full flex items-center justify-center mb-4 mx-auto shadow-xl"
              >
                <span className="text-3xl font-bold">K</span>
              </motion.div>
              
              <h3 className="text-lg font-semibold mb-1">Kenard Booker</h3>
              <p className="text-gray-400 text-xs mb-2">{userData?.name ? `Interviewing ${userData.name}` : 'Venture Capital Partner'}</p>
              
              {/* Speaking indicator */}
              <AnimatePresence>
                {isAISpeaking && (
                  <motion.div
                    initial={{ opacity: 0, y: 5 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: 5 }}
                    className="flex items-center justify-center space-x-1"
                  >
                    <div className="text-green-400 text-xs font-medium">Speaking</div>
                    <motion.div
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ repeat: Infinity, duration: 0.8 }}
                      className="w-1.5 h-1.5 bg-green-400 rounded-full"
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
            
            {/* VC label */}
            <div className="absolute bottom-3 right-3 bg-black/70 text-white px-2 py-1 rounded-md text-sm backdrop-blur-sm flex items-center space-x-1">
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z"></path>
                <path d="M19 10v2a7 7 0 0 1-14 0v-2"></path>
              </svg>
              <span>Kenard (CEO)</span>
            </div>
          </div>
        </div>
      </div>

      {/* Question display */}
      <AnimatePresence>
        {currentQuestion && isActive && (
          <motion.div 
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 0.9, y: 0 }}
            exit={{ opacity: 0, y: 10 }}
            transition={{ duration: 0.2 }}
            className="absolute bottom-24 left-1/2 transform -translate-x-1/2 bg-black/40 backdrop-blur-sm text-white px-4 py-2 rounded-full max-w-md shadow-sm border border-white/10"
          >
            <div className="flex items-center space-x-2">
              <div className="text-xs text-white/80 font-light">{currentQuestion}</div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Call controls */}
      {isActive && (
        <CallControls 
          isMuted={isMuted}
          isVideoOn={isVideoOn}
          isScreenSharing={isScreenSharing}
          onToggleMute={() => setIsMuted(!isMuted)}
          onToggleVideo={() => setIsVideoOn(!isVideoOn)}
          onToggleScreenShare={() => {
            setIsScreenSharing(!isScreenSharing);
            handleScreenShare();
          }}
          onEndCall={onEndCall}
        />
      )}

      {/* Only show a simple loading message when waiting for camera permission */}
      <AnimatePresence>
        {!isActive && permissionState === 'pending' && (
          <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-[#121212]/90 flex items-center justify-center"
          >
            <div className="text-white text-xl">Waiting for camera permission...</div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Show permission denied message */}
      <AnimatePresence>
        {renderPermissionDenied()}
      </AnimatePresence>
    </div>
  );

  // Helper function to render permission denied UI
  function renderPermissionDenied() {
    if (!isActive && permissionState === 'denied') {
      return (
        <motion.div 
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="absolute inset-0 bg-[#121212]/90 flex items-center justify-center p-6"
          >
            <div className="bg-[#1e1e1e] border border-red-500/30 rounded-2xl p-8 max-w-md w-full shadow-2xl text-center">
              <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-red-500">
                  <path d="M18 6 6 18"/><path d="m6 6 12 12"/>
                </svg>
              </div>
              <h3 className="text-xl font-bold text-white mb-2">Camera Access Required</h3>
              <p className="text-gray-400 mb-6">
                Please allow camera and microphone access in your browser settings to continue.
              </p>
              <button 
                onClick={initializeCamera}
                className="bg-[#6466E9] hover:bg-[#5254d7] text-white font-medium px-6 py-2 rounded-lg shadow-lg transition-colors"
              >
                Try Again
              </button>
            </div>
          </motion.div>
      );
    }
    return null;
  }
}

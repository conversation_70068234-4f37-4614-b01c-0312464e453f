import { useState, useEffect } from 'react';
import Image from 'next/image';
import { motion, AnimatePresence } from 'framer-motion';

interface CountdownTransitionProps {
  onComplete: () => void;
  userName: string;
}

export default function CountdownTransition({ onComplete, userName }: CountdownTransitionProps) {
  const [stage, setStage] = useState<'logo' | 'countdown' | 'complete'>('logo');
  const [count, setCount] = useState(3);

  useEffect(() => {
    // First show logo for 1.5 seconds
    const logoTimer = setTimeout(() => {
      setStage('countdown');
      
      // Start countdown
      const countdownInterval = setInterval(() => {
        setCount((prev) => {
          if (prev <= 1) {
            clearInterval(countdownInterval);
            setTimeout(() => {
              setStage('complete');
              onComplete();
            }, 800); // Show "Starting Interview" briefly before completing
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      
      return () => clearInterval(countdownInterval);
    }, 1500);
    
    return () => clearTimeout(logoTimer);
  }, [onComplete]);

  return (
    <AnimatePresence mode="wait">
      {stage === 'logo' && (
        <motion.div 
          key="logo"
          className="flex flex-col items-center justify-center"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 1.2 }}
          transition={{ duration: 0.5 }}
        >
          <Image src="/logo.svg" alt="Pitchly Logo" width={100} height={100} priority className="mb-6" />
          <motion.div 
            className="text-2xl font-medium text-white"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            Preparing your interview...
          </motion.div>
        </motion.div>
      )}
      
      {stage === 'countdown' && (
        <motion.div 
          key="countdown"
          className="flex flex-col items-center justify-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div 
            className="text-7xl font-bold text-[#6466E9]"
            initial={{ scale: 1.5, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.5 }}
          >
            {count}
          </motion.div>
          <motion.div 
            className="mt-4 text-xl text-gray-300"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            Get ready, {userName}
          </motion.div>
        </motion.div>
      )}
      
      {stage === 'complete' && (
        <motion.div 
          key="starting"
          className="flex flex-col items-center justify-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <motion.div 
            className="text-2xl font-semibold text-[#6466E9]"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            Starting Interview
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

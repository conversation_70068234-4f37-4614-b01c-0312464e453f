'use client';

import React from 'react';

interface CallTooltipProps {
  content: string;
  children: React.ReactNode;
}

export function CallTooltip({ content, children }: CallTooltipProps) {
  return (
    <div className="group relative">
      {children}
      <div className="absolute bottom-full mb-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none">
        <div className="bg-[#333333] text-white text-xs px-2 py-1 rounded whitespace-nowrap">
          {content}
        </div>
      </div>
    </div>
  );
}

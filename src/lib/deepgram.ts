// Deepgram integration utilities using REST API

// Interface for Deepgram transcription response
export interface DeepgramTranscriptionResponse {
  results: {
    channels: [
      {
        alternatives: [
          {
            transcript: string;
            confidence: number;
            words: {
              word: string;
              start: number;
              end: number;
              confidence: number;
            }[];
          }
        ];
      }
    ];
  };
}

// Interface for Deepgram transcription options
export interface DeepgramTranscriptionOptions {
  language?: string;
  model?: string;
  smart_format?: boolean;
  punctuate?: boolean;
  diarize?: boolean;
  detect_language?: boolean;
  utterances?: boolean;
}

// Default options for Deepgram transcription
export const defaultDeepgramOptions: DeepgramTranscriptionOptions = {
  language: 'en-US',
  model: 'nova-3',
  smart_format: true,
  punctuate: true,
  diarize: false,
  detect_language: false,
  utterances: true
};

// Function to transcribe audio using Deepgram REST API
export async function transcribeAudio(
  audioBlob: Blob,
  options: DeepgramTranscriptionOptions = defaultDeepgramOptions
): Promise<string> {
  try {
    // Create form data with audio file
    const formData = new FormData();
    formData.append('audio', audioBlob);
    
    // Send audio to our backend API endpoint
    const response = await fetch('/api/deepgram/transcribe', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error(`Transcription failed: ${response.statusText}`);
    }
    
    const result = await response.json();
    
    // Extract transcript from response
    if (result.results?.channels?.[0]?.alternatives?.[0]?.transcript) {
      return result.results.channels[0].alternatives[0].transcript;
    }
    
    return '';
  } catch (error) {
    console.error('Error transcribing audio:', error);
    throw error;
  }
}

// Convert audio data to proper format for Deepgram
export function processAudioData(audioData: Float32Array): Blob {
  // Convert Float32Array to Int16Array for better compatibility
  const pcmData = new Int16Array(audioData.length);
  
  for (let i = 0; i < audioData.length; i++) {
    // Convert float audio data to 16-bit PCM
    const s = Math.max(-1, Math.min(1, audioData[i]));
    pcmData[i] = s < 0 ? s * 0x8000 : s * 0x7FFF;
  }
  
  // Create WAV file with proper headers
  const wavBuffer = createWavFile(pcmData, 16000); // 16kHz sample rate
  
  // Convert to blob for upload
  return new Blob([wavBuffer], { type: 'audio/wav' });
}

// Create a WAV file with proper headers
function createWavFile(pcmData: Int16Array, sampleRate: number): ArrayBuffer {
  const headerSize = 44;
  const dataSize = pcmData.length * 2; // 16-bit = 2 bytes per sample
  const buffer = new ArrayBuffer(headerSize + dataSize);
  const view = new DataView(buffer);
  
  // RIFF identifier
  writeString(view, 0, 'RIFF');
  // File length
  view.setUint32(4, 36 + dataSize, true);
  // WAVE identifier
  writeString(view, 8, 'WAVE');
  // Format chunk identifier
  writeString(view, 12, 'fmt ');
  // Format chunk length
  view.setUint32(16, 16, true);
  // Sample format (1 = PCM)
  view.setUint16(20, 1, true);
  // Channels
  view.setUint16(22, 1, true);
  // Sample rate
  view.setUint32(24, sampleRate, true);
  // Byte rate (sample rate * block align)
  view.setUint32(28, sampleRate * 2, true);
  // Block align (channels * bytes per sample)
  view.setUint16(32, 2, true);
  // Bits per sample
  view.setUint16(34, 16, true);
  // Data chunk identifier
  writeString(view, 36, 'data');
  // Data chunk length
  view.setUint32(40, dataSize, true);
  
  // Write PCM data
  const pcmOffset = 44;
  for (let i = 0; i < pcmData.length; i++) {
    view.setInt16(pcmOffset + (i * 2), pcmData[i], true);
  }
  
  return buffer;
}

// Helper function to write strings to DataView
function writeString(view: DataView, offset: number, string: string): void {
  for (let i = 0; i < string.length; i++) {
    view.setUint8(offset + i, string.charCodeAt(i));
  }
}

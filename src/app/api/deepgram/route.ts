import { NextRequest, NextResponse } from 'next/server';

// This endpoint will generate a client token for Deepgram
export async function GET(req: NextRequest) {
  try {
    // In a production environment, you would want to validate the request
    // and possibly limit token generation based on user authentication
    
    const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;
    
    if (!DEEPGRAM_API_KEY) {
      return NextResponse.json(
        { error: 'Deepgram API key not configured' },
        { status: 500 }
      );
    }

    // For development purposes, we'll just pass the API key directly
    // In production, you should use the Deepgram SDK to generate proper tokens
    // or implement full JWT token generation
    
    // Return the API key as the token
    // The client will use this in the WebSocket protocol field
    const token = DEEPGRAM_API_KEY;

    return NextResponse.json({ token });
  } catch (error) {
    console.error('Error generating Deepgram token:', error);
    return NextResponse.json(
      { error: 'Failed to generate token' },
      { status: 500 }
    );
  }
}

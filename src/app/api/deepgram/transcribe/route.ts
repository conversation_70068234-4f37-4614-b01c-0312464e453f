import { NextRequest, NextResponse } from 'next/server';

// This endpoint handles audio transcription using Deepgram's REST API
export async function POST(req: NextRequest) {
  try {
    const DEEPGRAM_API_KEY = process.env.DEEPGRAM_API_KEY;
    
    if (!DEEPGRAM_API_KEY) {
      return NextResponse.json(
        { error: 'Deepgram API key not configured' },
        { status: 500 }
      );
    }

    // Get form data with audio file
    const formData = await req.formData();
    const audioFile = formData.get('audio');
    
    if (!audioFile || !(audioFile instanceof Blob)) {
      return NextResponse.json(
        { error: 'No audio file provided' },
        { status: 400 }
      );
    }

    // Convert to array buffer
    const arrayBuffer = await audioFile.arrayBuffer();
    
    // Call Deepgram API
    const response = await fetch('https://api.deepgram.com/v1/listen?model=nova-3&language=en-US&smart_format=true&punctuate=true&utterances=true', {
      method: 'POST',
      headers: {
        'Authorization': `Token ${DEEPGRAM_API_KEY}`,
        'Content-Type': 'audio/wav'
      },
      body: arrayBuffer
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error('Deepgram API error:', errorText);
      return NextResponse.json(
        { error: `Deepgram API error: ${response.status} ${response.statusText}` },
        { status: response.status }
      );
    }

    // Return the transcription result
    const result = await response.json();
    return NextResponse.json(result);
    
  } catch (error) {
    console.error('Error in transcription API:', error);
    return NextResponse.json(
      { error: `Transcription failed: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

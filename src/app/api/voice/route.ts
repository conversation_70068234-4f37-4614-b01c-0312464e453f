import { NextRequest, NextResponse } from 'next/server';

interface VoiceRequest {
  text: string;
  voice_id?: string;
  requestId?: string; // Add request ID for deduplication
  isPlayingAudio?: boolean; // Flag to indicate if audio is already playing on client
}

// Chris voice ID from ElevenLabs
const CHRIS_VOICE_ID = "iP95p4xoKVk53GoZ742B";

// Simple request cache to prevent duplicate voice synthesis
const requestCache = new Map<string, Promise<ArrayBuffer>>();

// Cache expiration time (5 minutes)
const CACHE_EXPIRATION = 5 * 60 * 1000;

// Track active synthesis requests to prevent duplicates
const activeRequests = new Set<string>();

export async function POST(request: NextRequest) {
  try {
    const { text, voice_id = CHRIS_VOICE_ID, requestId, isPlayingAudio }: VoiceRequest = await request.json();
    
    // Create a cache key from the text and voice ID
    const cacheKey = `${text}_${voice_id}_${requestId || ''}`;
    
    console.log(`Voice API received request: ${cacheKey}${isPlayingAudio ? ' (audio already playing)' : ''}`);
    
    // Check if we have a cached response for this exact request
    if (requestId && requestCache.has(cacheKey)) {
      console.log('Using cached voice synthesis for:', cacheKey);
      const cachedAudio = await requestCache.get(cacheKey);
      
      return new NextResponse(cachedAudio, {
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Length': cachedAudio!.byteLength.toString(),
          'Cache-Control': 'public, max-age=31536000',
          'X-Cache': 'HIT'
        },
      });
    }
    
    // Check if this exact request is already being processed
    if (activeRequests.has(cacheKey)) {
      console.log('Duplicate request detected, waiting for existing request to complete:', cacheKey);
      
      try {
        // Instead of returning an error, wait for the existing request to complete
        // and then return the cached result
        await new Promise<void>((resolve) => {
          const checkCache = () => {
            if (requestCache.has(cacheKey)) {
              resolve();
            } else if (!activeRequests.has(cacheKey)) {
              // The request failed or was removed without caching
              resolve();
            } else {
              // Check again in 100ms
              setTimeout(checkCache, 100);
            }
          };
          checkCache();
        });
        
        // Now check if we have a cached response
        if (requestCache.has(cacheKey)) {
          console.log('Using cached voice synthesis after waiting:', cacheKey);
          const cachedAudio = await requestCache.get(cacheKey);
          
          return new NextResponse(cachedAudio, {
            headers: {
              'Content-Type': 'audio/mpeg',
              'Content-Length': cachedAudio!.byteLength.toString(),
              'Cache-Control': 'public, max-age=31536000',
              'X-Cache': 'HIT-AFTER-WAIT'
            },
          });
        }
        
        // If we get here, the original request failed but was removed from activeRequests
        console.log('Original request failed, proceeding with new request:', cacheKey);
      } catch (error) {
        console.error('Error waiting for existing request:', error);
        // Continue with the request anyway
      }
    }

    if (!text) {
      return NextResponse.json(
        { error: 'Text is required' },
        { status: 400 }
      );
    }

    if (!process.env.ELEVENLABS_API_KEY) {
      throw new Error('ElevenLabs API key not configured');
    }

    // Add this request to active requests set
    if (requestId) {
      activeRequests.add(cacheKey);
      console.log(`Added request to active set: ${cacheKey}`);
    }
    
    let audioBuffer: ArrayBuffer;
    
    try {
      console.log(`Sending request to ElevenLabs API for voice ${voice_id} with text: "${text.substring(0, 30)}${text.length > 30 ? '...' : ''}"`);
      console.log(`Using API key: ${process.env.ELEVENLABS_API_KEY ? process.env.ELEVENLABS_API_KEY.substring(0, 5) + '...' : 'undefined'}`);
      
      const response = await fetch(`https://api.elevenlabs.io/v1/text-to-speech/${voice_id}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': process.env.ELEVENLABS_API_KEY,
        },
        body: JSON.stringify({
          text,
          model_id: "eleven_flash_v2_5",
          voice_settings: {
            stability: 0.5,
            similarity_boost: 0.5,
            style: 0.0,
            use_speaker_boost: true
          }
        }),
      });

      console.log(`ElevenLabs API response status: ${response.status} ${response.statusText}`);
      console.log(`Response headers:`, Object.fromEntries([...response.headers.entries()]));

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`ElevenLabs API error response body: ${errorText}`);
        throw new Error(`ElevenLabs API error: ${response.status} ${response.statusText} - ${errorText}`);
      }

      audioBuffer = await response.arrayBuffer();
      console.log(`Received audio buffer of size: ${audioBuffer.byteLength} bytes`);
      
      // Store in cache if we have a requestId
      if (requestId) {
        requestCache.set(cacheKey, Promise.resolve(audioBuffer));
        
        // Set timeout to clear the cache entry
        setTimeout(() => {
          requestCache.delete(cacheKey);
        }, CACHE_EXPIRATION);
      }
      
      return new NextResponse(audioBuffer, {
        headers: {
          'Content-Type': 'audio/mpeg',
          'Content-Length': audioBuffer.byteLength.toString(),
          'Cache-Control': 'public, max-age=31536000',
          'X-Cache': 'MISS'
        },
      });
    } finally {
      // Remove this request from active requests set when done
      if (requestId) {
        activeRequests.delete(cacheKey);
        console.log(`Removed request from active set: ${cacheKey}`);
      }
    }
  } catch (error: unknown) {
    console.error('Voice API error:', error);
    return NextResponse.json(
      { error: `Failed to synthesize voice: ${error instanceof Error ? error.message : 'Unknown error'}` },
      { status: 500 }
    );
  }
}

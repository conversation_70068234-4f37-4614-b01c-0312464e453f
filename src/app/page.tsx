'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Image from 'next/image';

export default function Home() {
  const [name, setName] = useState('');
  const [startupName, setStartupName] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  const handleStartPitch = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim() || !startupName.trim()) return;

    setIsLoading(true);

    // Store user data in sessionStorage for the call
    sessionStorage.setItem('pitchly_user', JSON.stringify({
      name: name.trim(),
      startupName: startupName.trim()
    }));

    // Navigate to call page
    router.push('/call');
  };

  return (
    <div className="min-h-screen bg-[#121212] flex flex-col items-center justify-center p-4">
      {/* Background gradient effect */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-[-10%] right-[-10%] w-[50%] h-[50%] rounded-full bg-[#6466E9]/20 blur-[100px]"></div>
        <div className="absolute bottom-[-10%] left-[-10%] w-[50%] h-[50%] rounded-full bg-[#6466E9]/10 blur-[100px]"></div>
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8 }}
        className="max-w-md w-full z-10"
      >
        <div className="text-center mb-8">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.1 }}
            className="flex justify-center mb-6"
          >
            <Image src="/logo.svg" alt="Pitchly Logo" width={60} height={60} priority />
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-5xl font-bold text-white mb-4"
          >
            Pitchly
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3 }}
            className="text-gray-300 text-lg"
          >
            Practice your VC pitch with AI-powered feedback
          </motion.p>
        </div>

        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          onSubmit={handleStartPitch}
          className="bg-white/5 backdrop-blur-lg rounded-2xl p-8 border border-white/10 shadow-xl"
        >
          <div className="space-y-6">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-200 mb-2">
                Your Name
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#6466E9] focus:border-transparent transition-all duration-200"
                placeholder="Enter your name"
                required
              />
            </div>

            <div>
              <label htmlFor="startup" className="block text-sm font-medium text-gray-200 mb-2">
                Startup Name
              </label>
              <input
                type="text"
                id="startup"
                value={startupName}
                onChange={(e) => setStartupName(e.target.value)}
                className="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#6466E9] focus:border-transparent transition-all duration-200"
                placeholder="Enter your startup name"
                required
              />
            </div>

            <motion.button
              type="submit"
              disabled={isLoading || !name.trim() || !startupName.trim()}
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              className="w-full bg-[#6466E9] text-white font-semibold py-3 px-6 rounded-lg hover:bg-[#5254d7] focus:outline-none focus:ring-2 focus:ring-[#6466E9] focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 shadow-lg shadow-[#6466E9]/20"
            >
              {isLoading ? 'Starting...' : 'Start Your Pitch'}
            </motion.button>
          </div>
        </motion.form>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-8 text-gray-400 text-sm"
        >
          <p>Get ready for a realistic VC interview simulation</p>
        </motion.div>
      </motion.div>
    </div>
  );
}

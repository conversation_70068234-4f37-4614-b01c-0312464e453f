'use client';

import { useState, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import VideoCall from '@/components/VideoCall';
import AudioHandler, { AudioHandlerRef } from '@/components/AudioHandler';
import CountdownTransition from '@/components/CountdownTransition';
import { UserData, CallState, TranscriptEntry, ESCALATION_LEVELS } from '@/types';
import { generateSessionId } from '@/lib/utils';

export default function CallPage() {
  const [userData, setUserData] = useState<UserData | null>(null);
  const [callState, setCallState] = useState<CallState>({
    isActive: false,
    currentLevel: 1,
    isListening: true,
    isAISpeaking: false,
    transcript: [],
    currentQuestion: "",
    sessionId: generateSessionId()
  });
  const [error, setError] = useState<string>('');
  const [showCountdown, setShowCountdown] = useState(false);
  const router = useRouter();

  // Create ref for AudioHandler
  const audioHandlerRef = useRef<AudioHandlerRef>(null);

  useEffect(() => {
    // Get user data from sessionStorage
    const storedData = sessionStorage.getItem('pitchly_user');
    if (!storedData) {
      router.push('/');
      return;
    }
    setUserData(JSON.parse(storedData));
  }, [router]);

  // This function is no longer used directly - we use handleCountdownComplete instead
  const startCall = async () => {
    // Function kept for reference but no longer called directly
    console.log("This function should not be called directly anymore");
  };

  const speakQuestion = async (question: string) => {
    try {
      // Temporarily pause listening while AI speaks to avoid picking up audio playback
      if (audioHandlerRef.current) {
        audioHandlerRef.current.stopListening();
      }
      
      // Set AI speaking state
      setCallState(prev => ({
        ...prev,
        isAISpeaking: true
      }));
      
      // Use the ref to synthesize speech
      if (audioHandlerRef.current) {
        await audioHandlerRef.current.synthesizeWithElevenLabs(question);
      }
    } catch (error) {
      console.error('Voice synthesis error:', error);
    }

    // Mark AI speech as finished and resume listening
    setCallState(prev => ({
      ...prev,
      isAISpeaking: false
    }));
    
    // Resume listening after a short delay to avoid picking up audio playback echo
    setTimeout(() => {
      if (audioHandlerRef.current) {
        audioHandlerRef.current.startListening();
      }
    }, 300);
  };

  const handleSpeechResult = async (transcript: string) => {
    if (!userData) return;

    // Add user message to transcript
    const userMessage: TranscriptEntry = {
      speaker: 'user',
      message: transcript,
      timestamp: Date.now()
    };

    setCallState(prev => ({
      ...prev,
      transcript: [...prev.transcript, userMessage]
    }));

    // Get AI response
    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [
            { role: 'user', content: transcript }
          ],
          currentLevel: callState.currentLevel,
          userName: userData.name,
          startupName: userData.startupName
        })
      });

      const data = await response.json();

      if (data.message) {
        // Combine all state updates into a single call to avoid multiple renders
        setCallState(prev => ({
          ...prev,
          currentQuestion: data.message,
          isAISpeaking: true,
          currentLevel: data.shouldEscalate ? data.nextLevel : prev.currentLevel,
          // Add AI response to transcript in the same update
          transcript: [...prev.transcript, {
            speaker: 'ai',
            message: data.message,
            timestamp: Date.now()
          }]
        }));

        // Only speak once after state is updated
        await speakQuestion(data.message);
      }
    } catch (error) {
      console.error('Chat API error:', error);
      setError('Failed to get AI response');
    }
  };

  const handleSpeechEnd = () => {
    // Speech recognition ended
  };

  const handleAudioError = (error: string) => {
    setError(error);
  };

  const endCall = async () => {
    if (!userData) return;

    // Save session data
    const sessionData = {
      userName: userData.name,
      startupName: userData.startupName,
      transcript: callState.transcript,
      duration: Math.floor((Date.now() - (callState.transcript[0]?.timestamp || Date.now())) / 1000),
      startTime: callState.transcript[0]?.timestamp || Date.now(),
      endTime: Date.now()
    };

    try {
      const response = await fetch('/api/session', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(sessionData)
      });

      const results = await response.json();

      // Store results for the results page
      sessionStorage.setItem('pitchly_results', JSON.stringify(results));

    } catch (error) {
      console.error('Session save error:', error);
    }

    setCallState(prev => ({ ...prev, isActive: false }));
    router.push('/results');
  };

  if (!userData) {
    return (
      <div className="min-h-screen bg-[#121212] flex items-center justify-center">
        <div className="text-white text-xl">Loading...</div>
      </div>
    );
  }

  // When the VideoCall component auto-triggers this function, we show the countdown
  const handleStartButtonClick = () => {
    setShowCountdown(true);
  };
  
  // After countdown completes, we actually start the call
  const handleCountdownComplete = () => {
    // Set call to active without triggering the speech immediately
    setCallState(prev => ({
      ...prev,
      isActive: true
    }));
    
    // Slight delay before starting the actual interview
    setTimeout(() => {
      if (!userData) return;
      
      // Start with first question
      const firstQuestion = `Hello ${userData.name}, I'm excited to hear about ${userData.startupName}. ${ESCALATION_LEVELS[0].questions[0]}`;
  
      // Update state with the question and set AI as speaking
      setCallState(prev => ({
        ...prev,
        currentQuestion: firstQuestion,
        isAISpeaking: true,
        // Add AI message to transcript in the same state update
        transcript: [...prev.transcript, {
          speaker: 'ai',
          message: firstQuestion,
          timestamp: Date.now()
        }]
      }));
  
      // Only speak the question once
      speakQuestion(firstQuestion);
    }, 1000);
  };

  return (
    <div className="h-screen bg-[#121212] flex flex-col overflow-hidden">

      {/* Error display */}
      {error && (
        <div className="bg-red-600 text-white p-3 text-center">
          {error}
          <button
            onClick={() => setError('')}
            className="ml-4 underline"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Main content - either countdown or video call */}
      <div className="flex-1 relative">
        {/* Video Call Component - Always rendered but may be hidden */}
        <div className={showCountdown && !callState.isActive ? 'hidden' : 'h-full'}>
          <VideoCall
            isActive={callState.isActive}
            currentQuestion={callState.currentQuestion}
            onStartCall={handleStartButtonClick}
            onEndCall={endCall}
            userData={userData}
            currentLevel={callState.currentLevel}
            levelTitle={ESCALATION_LEVELS[callState.currentLevel - 1]?.title}
          />
        </div>
        
        {/* Countdown overlay - shown on top when needed */}
        {showCountdown && !callState.isActive && (
          <div className="absolute inset-0 z-50 flex items-center justify-center bg-[#121212]">
            <CountdownTransition onComplete={handleCountdownComplete} userName={userData.name} />
          </div>
        )}
      </div>

      {/* Audio Handler Component */}
      <AudioHandler
        ref={audioHandlerRef}
        isListening={callState.isListening}
        onSpeechResult={handleSpeechResult}
        onSpeechEnd={() => console.log('Speech ended')}
        onError={(err) => setError(err)}
      />
    </div>
  );
}
